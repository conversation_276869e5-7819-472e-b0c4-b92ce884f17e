var sidebarFull=200,sidebarCollapsed=80,LOGO_COLOR="#43a2ad",OLD_STUDENTS_FEMALE_COLOR="#bfe9ef",PAST_DAYS=1825;function registerSidebarMenu(){registerHomeMenu(),registerChangePassword(),menuLoader.registerSidebarMenu()}function registerHomeMenu(){$("#homeNav").on("click",function(){loadHomePage()})}function registerChangePassword(){$("#changePasswordNav").on("click",function(){loadChangePasswordPage()})}function loadHomePage(){ajaxClient.get("/organisation-portal/home",function(t){$("#main-content").html(t),$("input#stats-date").daterangepicker({autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("day").subtract(PAST_DAYS,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"},onSelect:function(t){$(this).change()}}).on("change",function(){homePage.refreshHomePage()}),homePage.displayDashboardContent(),academicSessionHandler.bindSessionChangeEvent(homePage.refreshHomePage)})}$(document).ready(function(){registerSidebarMenu(),$("#sidebar").addClass("collapsed"),registerChangePassword(),$("#bell-notification-li").css("display","none")});var homePageV2={chartInstances:{},loadHomePage:function(){ajaxClient.get("/organisation-portal/home/<USER>",function(t){$("#main-content").html(t),initSelect2(),$("input#stats-date-range").daterangepicker({autoApply:!1,showDropdowns:!0,startDate:moment().startOf("day"),endDate:moment().startOf("day"),minDate:moment().startOf("day").subtract(PAST_DAYS,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"},ranges:{Today:[moment(),moment()],Yesterday:[moment().subtract(1,"days"),moment().subtract(1,"days")],"Last 7 Days":[moment().subtract(6,"days"),moment()],"Last 30 Days":[moment().subtract(29,"days"),moment()],"This Month":[moment().startOf("month"),moment().endOf("month")],"Last Month":[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")]}}).on("change",function(){homePageV2.loadWidgets()}),homePageV2.loadWidgets(),homePageV2.initializeBirthdayWidget()})},destroyChart:function(t){homePageV2.chartInstances[t]&&(homePageV2.chartInstances[t].destroy(),delete homePageV2.chartInstances[t])},destroyAllCharts:function(){Object.keys(homePageV2.chartInstances).forEach(function(t){homePageV2.destroyChart(t)})},loadWidget:function(t,e,a){for(var n in t)console.log("displaying loader for : "+n),homePageV2.displayWidgetLoader(t[n]);ajaxClient.get(e,function(e){for(var n in a(t,e),t)homePageV2.hideWidgetLoader(t[n])},!0)},loadWidgets:function(){homePageV2.destroyAllCharts();var t=$("#select-institutes").val(),e=$("#stats-date-range").val(),a=getDate(e.split(" - ")[0]).getTime()/1e3,n=moment(e.split(" - ")[1],inputDateParserFormat).add(1,"days"),o=getDayStart(n.toDate()).getTime()/1e3-1,s=Array.isArray(t)?t.join(","):t,r="?selectedInstitutes="+encodeURIComponent(s)+"&startDate="+a+"&endDate="+o;console.log(t),console.log(a+" - "+o),console.log(r),homePageV2.loadWidget({studentAttendanceWidgetClass:".student-attendance-stats",studentCountWidgetClass:".student-count-stats",studentCountDistributionWidgetClass:".student-count-distribution-stats"},"/organisation-portal/stats/v2/student-attendance"+r,homePageV2.renderStudentAttendanceAndMetadata),homePageV2.loadWidget({staffCountWidgetClass:".staff-count-stats",staffAttendanceWidgetClass:".staff-attendance-stats",staffGenderDistributionWidgetClass:".staff-gender-distribution-stats"},"/organisation-portal/stats/v2/staff-attendance"+r,homePageV2.renderStaffAttendance),homePageV2.loadWidget({studentAdmissionWidgetClass:".student-admission-stats"},"/organisation-portal/stats/v2/student-admission"+r,homePageV2.renderStudentAdmissionStats),homePageV2.loadWidget({totalFeeCollectionWidgetClass:".fee-collection-stats",feeCollectionPaymentModeDistributionWidgetClass:".fee-collection-payment-mode-distribution-stats",feeCollectionFeeHeadDistributionWidgetClass:".fee-collection-fee-head-distribution-stats"},"/organisation-portal/stats/v2/fee-collection"+r,homePageV2.renderFeeCollectionStats),homePageV2.filterBirthdayWidget(t),homePageV2.loadWidget({activeStudentTransportWidgetClass:".active-transport-stats"},"/organisation-portal/stats/v2/active-student-transport"+r,homePageV2.renderActiveStudentTransport)},displayWidgetLoader:function(t){$(t).find(".stats-widget-loader").attr("style","display:block;"),$(t).find(".stats-widget-content").attr("style","display:none;")},hideWidgetLoader:function(t){$(t).find(".stats-widget-loader").attr("style","display:none;"),$(t).find(".stats-widget-content").attr("style","display:block;")},renderStudentAttendanceAndMetadata:function(t,e){var a=t.studentAttendanceWidgetClass,n=t.studentCountWidgetClass;$(n).find(".stats-widget-content").text(e.totalStudent);studentAttendanceCounts=e.studentAttendanceCounts[0];for(var o=0;o<studentAttendanceCounts.attendanceCounts.length;o++)"PRESENT"==studentAttendanceCounts.attendanceCounts[o].attendanceStatus&&(studentAttendanceCounts.attendanceCounts[o].count,studentAttendanceCounts.attendanceCounts[o].totalCount),"LEAVE"==studentAttendanceCounts.attendanceCounts[o].attendanceStatus&&(studentAttendanceCounts.attendanceCounts[o].count,studentAttendanceCounts.attendanceCounts[o].totalCount);const s=$('\n        <div id="carouselExampleControls" class="carousel slide" data-ride="carousel">\n          <div class="carousel-inner"></div>\n          <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">\n            <span class="carousel-control-prev-icon" aria-hidden="true" style="background-image: url(\'data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'black\' viewBox=\'0 0 8 8\'%3E%3Cpath d=\'M5.25 0L3.82 1.41 6.41 4 3.82 6.59 5.25 8 9.25 4z\'/%3E%3C/svg%3E\');"></span>\n            <span class="sr-only">Previous</span>\n          </a>\n          <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">\n            <span class="carousel-control-next-icon" aria-hidden="true" style="background-image: url(\'data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'black\' viewBox=\'0 0 8 8\'%3E%3Cpath d=\'M2.75 0L4.18 1.41 1.59 4 4.18 6.59 2.75 8 -1.25 4z\'/%3E%3C/svg%3E\');"></span>\n            <span class="sr-only">Next</span>\n          </a>\n        </div>\n      '),r=s.find(".carousel-inner");e.studentAttendanceCounts.forEach((t,a)=>{var n=t.attendanceType;const o=t.attendanceCounts.find(t=>"PRESENT"===t.attendanceStatus);t.attendanceCounts.find(t=>"LEAVE"===t.attendanceStatus);0==o.total?percentPresent=0:percentPresent=(o.count/o.total*100).toFixed(1);const s=$(`\n          <div class="carousel-item ${0===a?"active":""}">\n            <div class="card-body py-4">\n              <div class="media">\n                <div class="media-body">\n                  <h4 class="mb-2 stats-widget-content">${o.count} / ${o.total} (${percentPresent}%)</h4>\n                  <p class="mb-2">Student Attendance</p>\n                  <div class="mb-0 mt-3" >\n                    <span class="badge badge-soft-success" >\n                      <i class="mdi mdi-arrow-bottom-right"></i>\n                      ${n}\n                    </span>\n                  </div>\n                </div>\n                <div class="d-inline-block ml-3">\n                  <div class="stat">\n                    \x3c!-- <span class="iconify" data-icon="bx-bx-rupee" data-inline="false"></span> --\x3e\n                    <i class="align-middle text-success" data-feather="user-check"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        `);r.append(s);var d=[],i=[],c=0;e.studentCount.forEach(t=>{var e=t.instituteName,a=t.value;d.push(e),i.push(a),c+=a}),homePageV2.renderStudentCountPieChart(d,i,c),homePageV2.renderStudentInstituteCountTable(d,i,c)}),$(a).find(".stats-widget-content").html(s),window.feather&&feather.replace()},renderStaffAttendance:function(t,e){var a=t.staffAttendanceWidgetClass,n=t.staffCountWidgetClass,o=0,s=0,r=0;if(e.staffAttendanceCounts&&e.staffAttendanceCounts.length>0){var d=e.staffAttendanceCounts[0].attendanceCounts,i=d.find(t=>"PRESENT"===t.attendanceStatus);i&&(o=i.count,r=i.total);var c=d.find(t=>"LEAVE"===t.attendanceStatus);c&&(s=c.count)}var l=[],u=[],h=[],g={},f=[...new Set(e.staffCountByGender.map(t=>t.instituteName))];f.forEach(t=>{g[t]={male:0,female:0}}),e.staffCountByGender.forEach(t=>{"MALE"===t.gender?g[t.instituteName].male=t.count:"FEMALE"===t.gender&&(g[t.instituteName].female=t.count)}),f.forEach(t=>{l.push(t),u.push(g[t].male),h.push(g[t].female)});$(n).find(".stats-widget-content").text(r),$(a).find(".stats-widget-content-staff-present").text(o),$(a).find(".stats-widget-content-staff-leave").text(s),homePageV2.renderStaffGenderDistributionChart(l,u,h)},renderStudentAdmissionStats:function(t,e){console.log(e),console.log("-----------------------");var a=t.studentAdmissionWidgetClass;$(a).find(".stats-widget-content-admission").text(e.totalNewAdmissions),$(a).find(".stats-widget-content-tc").text(e.totalTCIssued)},renderFeeCollectionStats:function(t,e){var a=t.totalFeeCollectionWidgetClass;$(a).find(".stat-value").text(formatINRCurrency(e.totalAmount));var n=[],o=[];e.paymentModeCollections&&Array.isArray(e.paymentModeCollections)&&e.paymentModeCollections.forEach(function(t){n.push(t.modeDisplayName),o.push(t.value)});var s=[],r=[],d=[],i={};if(e.instituteStats&&Array.isArray(e.instituteStats)){var c=["#7bc4ce","#a8d5a8","#f4c2a1","#d4a5d4","#f4d03f","#85c1e9","#f1948a","#aed6f1","#d5dbdb","#f8c471","#c39bd3","#82e0aa","#f9e79f","#85c1e9","#f5b7b1","#a9dfbf","#f8d7da","#b3e5fc","#dcedc8","#fff3e0","#e1f5fe","#f3e5f5","#e8f5e8"];e.instituteStats.forEach(function(t){s.push(t.instituteName),t.feeHeadCollections&&Array.isArray(t.feeHeadCollections)&&t.feeHeadCollections.forEach(function(t){-1===d.indexOf(t.feeHead)&&(d.push(t.feeHead),i[t.feeHead]=c[(d.length-1)%c.length])})}),d.forEach(function(t){var a=[];e.instituteStats.forEach(function(e){var n=0;if(e.feeHeadCollections&&Array.isArray(e.feeHeadCollections)){var o=e.feeHeadCollections.find(function(e){return e.feeHead===t});o&&(n=o.value)}a.push(n)}),r.push({label:t,backgroundColor:i[t],borderColor:i[t],hoverBackgroundColor:i[t],hoverBorderColor:i[t],data:a,barPercentage:.5,categoryPercentage:.5})})}homePageV2.renderFeePaymentModeCollectionPieChart(n,o,formatINRCurrency(e.totalAmount)),homePageV2.renderFeePaymentModeCollectionTable(n,o,e.totalAmount),homePageV2.renderFeeCollectionFeeHeadDistributionChart(s,r)},renderStudentCountPieChart:function(t,e,a){homePageV2.destroyChart("student-count-pie");homePageV2.chartInstances["student-count-pie"]=new Chart($("#chartjs-student-count-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:["#7bc4ce","#a8d5a8","#f4c2a1","#d4a5d4","#f4d03f","#85c1e9","#f1948a","#aed6f1","#d5dbdb","#f8c471"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:t=>{var e=t.chart.width,n=t.chart.height,o=t.chart.ctx;o.restore();var s=(n/114).toFixed(2);o.font=s+"em sans-serif",o.textBaseline="middle";var r=a,d=Math.round((e-o.measureText(r).width)/2),i=n/2;o.fillText(r,d,i),o.save()}}]})},renderStudentInstituteCountTable:function(t,e,a){var n='<table class="table mb-0">';n+="<thead>",n+="<tr>",n+="<th>Institute</th>",n+='<th class="text-center">Student Count</th>',n+='<th class="text-center">Percentage</th>',n+="</tr>",n+="</thead>",n+="<tbody>";for(var o=["#7bc4ce","#a8d5a8","#f4c2a1","#d4a5d4","#f4d03f","#85c1e9","#f1948a","#aed6f1","#d5dbdb","#f8c471"],s=0;s<t.length;s++){var r=a>0?(e[s]/a*100).toFixed(1):0;n+="<tr>",n+='<td><i class="fas fa-square-full" style="color: '+o[s%o.length]+'"></i>&nbsp;&nbsp;'+t[s]+"</td>",n+='<td class="text-center">'+e[s]+"</td>",n+='<td class="text-center">'+r+"%</td>",n+="</tr>"}n+='<tr style="font-weight: bold;">',n+="<td>TOTAL</td>",n+='<td class="text-center">'+a+"</td>",n+='<td class="text-center">100%</td>',n+="</tr>",n+="</tbody>",n+="</table>",$(".student-institute-count-table").html(n)},renderFeePaymentModeCollectionTable:function(t,e,a){var n='<table class="table mb-0">';n+="<thead>",n+="<tr>",n+="<th>Payment Mode</th>",n+='<th class="text-center">Amount</th>',n+='<th class="text-center">Percentage</th>',n+="</tr>",n+="</thead>",n+="<tbody>";for(var o=["#7bc4ce","#a8d5a8","#f4c2a1","#d4a5d4","#f4d03f","#85c1e9","#f1948a","#aed6f1","#d5dbdb","#f8c471"],s=0;s<t.length;s++){var r=a>0?(e[s]/a*100).toFixed(1):0;n+="<tr>",n+='<td><i class="fas fa-square-full" style="color: '+o[s%o.length]+'"></i>&nbsp;&nbsp;'+t[s]+"</td>",n+='<td class="text-center">₹'+e[s].toLocaleString()+"</td>",n+='<td class="text-center">'+r+"%</td>",n+="</tr>"}n+='<tr style="font-weight: bold;">',n+="<td>TOTAL</td>",n+='<td class="text-center">₹'+a.toLocaleString()+"</td>",n+='<td class="text-center">100%</td>',n+="</tr>",n+="</tbody>",n+="</table>",$(".fee-collection-institute-table").html(n)},renderStaffGenderDistributionChart:function(t,e,a){homePageV2.destroyChart("staff-gender-distribution"),homePageV2.chartInstances["staff-gender-distribution"]=new Chart($("#chartjs-staff-gender-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Male",backgroundColor:"#5cb3bf",borderColor:"#5cb3bf",hoverBackgroundColor:"#5cb3bf",hoverBorderColor:"#5cb3bf",data:e,barPercentage:.325,categoryPercentage:.5},{label:"Female",backgroundColor:"#f4a6cd",borderColor:"#f4a6cd",hoverBackgroundColor:"#f4a6cd",hoverBorderColor:"#f4a6cd",data:a,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderFeePaymentModeCollectionPieChart:function(t,e,a){homePageV2.destroyChart("fee-collection-payment-mode-pie");homePageV2.chartInstances["fee-collection-payment-mode-pie"]=new Chart($("#chartjs-fee-collection-payment-mode-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:["#7bc4ce","#a8d5a8","#f4c2a1","#d4a5d4","#f4d03f","#85c1e9","#f1948a","#aed6f1","#d5dbdb","#f8c471"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:t=>{var e=t.chart.width,n=t.chart.height,o=t.chart.ctx;o.restore();var s=(n/114).toFixed(2);o.font=s+"em sans-serif",o.textBaseline="middle";var r=a,d=Math.round((e-o.measureText(r).width)/2),i=n/2;o.fillText(r,d,i),o.save()}}]})},renderFeeCollectionFeeHeadDistributionChart:function(t,e){console.log("Institute Labels:",t),console.log("Fee Head Datasets:",e),homePageV2.destroyChart("fee-collection-fee-head-distribution"),homePageV2.chartInstances["fee-collection-fee-head-distribution"]=new Chart($("#chartjs-fee-collection-fee-head-distribution"),{type:"horizontalBar",data:{labels:t,datasets:e},options:{maintainAspectRatio:!1,legend:{display:!0,position:"top",align:"start",labels:{boxWidth:12}},scales:{xAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!0}],yAxes:[{stacked:!0,gridLines:{color:"transparent"}}]},tooltips:{mode:"index",intersect:!1,callbacks:{title:function(t,e){return"Institute: "+e.labels[t[0].index]},label:function(t,e){var a=e.datasets[t.datasetIndex].label||"";return a&&(a+=": "),a+=t.xLabel.toLocaleString()},footer:function(t){var e=0;return t.forEach(function(t){e+=t.xLabel}),"Total: "+e.toLocaleString()}}}}})},renderActiveStudentTransport:function(t,e){console.log("Rendering Active Student Transport:",e);var a=e.transportAssignedStudents,n=e.totalTransportAssignedStudents,o=[],s=[];a.forEach(t=>{o.push(t.instituteName),s.push(t.value)}),homePageV2.renderActiveStudentTransportPieChart(o,s,n),homePageV2.renderActiveStudentTransportTable(o,s,n)},renderActiveStudentTransportPieChart:function(t,e,a){homePageV2.destroyChart("active-student-transport-pie");homePageV2.chartInstances["active-student-transport-pie"]=new Chart($("#chartjs-active-student-transport-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:["#7bc4ce","#a8d5a8","#f4c2a1","#d4a5d4","#f4d03f","#85c1e9","#f1948a","#aed6f1","#d5dbdb","#f8c471"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:t=>{var e=t.chart.width,n=t.chart.height,o=t.chart.ctx;o.restore();var s=(n/114).toFixed(2);o.font=s+"em sans-serif",o.textBaseline="middle";var r=a,d=Math.round((e-o.measureText(r).width)/2),i=n/2;o.fillText(r,d,i),o.save()}}]})},renderActiveStudentTransportTable:function(t,e,a){var n=["#7bc4ce","#a8d5a8","#f4c2a1","#d4a5d4","#f4d03f","#85c1e9","#f1948a","#aed6f1","#d5dbdb","#f8c471"],o='<table class="table table-sm">';o+="<thead>",o+="<tr>",o+="<th>Institute</th>",o+='<th class="text-center">Students</th>',o+='<th class="text-center">%</th>',o+="</tr>",o+="</thead>",o+="<tbody>";for(var s=0;s<t.length;s++){var r=a>0?(e[s]/a*100).toFixed(1):0;o+="<tr>",o+='<td><span class="dot" style="background-color: '+n[s%n.length]+';"></span> '+t[s]+"</td>",o+='<td class="text-center">'+e[s].toLocaleString()+"</td>",o+='<td class="text-center">'+r+"%</td>",o+="</tr>"}o+='<tr style="font-weight: bold;">',o+="<td>TOTAL</td>",o+='<td class="text-center">'+a.toLocaleString()+"</td>",o+='<td class="text-center">100%</td>',o+="</tr>",o+="</tbody>",o+="</table>",$(".active-student-transport-institute-table").html(o)}},homePage={refreshHomePage:function(){var t=academicSessionHandler.getSelectedSessionId(),e=getDate($("input#stats-date").val()).getTime()/1e3;ajaxClient.get("/organisation-portal/home-date-session-change/"+e+"/"+t,function(t){$("#attendance-dashboard-session-content").html(t),homePage.displayDashboardContent()})},displayDashboardContent:function(){var t=readJson("#home-page-stats"),e=[],a=[],n=[],o=[],s={},r=0,d=[],i=0,c=[],l=[],u=[],h=[],g=[],f=[],m=[];console.log("test");var p=[],b=0;for(instituteId in t){var C=t[instituteId];institute=C.institute;var v=institute.branchName,y=institute.instituteUniqueCode;null!=v&&""!=v||(v=institute.instituteName),e.push(v);var P=y+":"+v;n.push(P),a.push(C.total_students);var w=C.attendance_stats,x=0;null!=w&&(x=parseFloat(w.totalPresentAttendance.toFixed(2))),r+=x,d.push(parseFloat(x.toFixed(2)));var A=C.transport_stats,S=0;null!=A&&(S=A.totalTransportAssignedStudentCount),c.push(S),i+=S;var L=C.staff_stats;if(null!=L){b+=L.todayTotalPresentStaff,p.push(L.todayTotalPresentStaff);var k=L.staffGenderWiseCount;if(null!=k)for(var O=Object.keys(k),$=0;$<O.length;$++){var E=O[$];"MALE"===E?l.push(k[E]):"FEMALE"===E&&u.push(k[E])}}var F=C.class_payment_stats;if(null!=F){h.push(parseFloat(F.assignedAmount.toFixed(2))),g.push(parseFloat(F.collectedAmount.toFixed(2))),f.push(parseFloat(F.discountAmount.toFixed(2))),m.push(parseFloat(F.dueAmount.toFixed(2)));var D=F.feeHeadCollectedAmountMap;for(const[t,e]of Object.entries(D))s[t]||(o.push(t),s[t]={}),s[t][P]||(s[t][P]=0),s[t][P]+=e}else h.push(0),g.push(0),f.push(0),m.push(0)}homePage.renderStudentCountChart(e,a),homePage.renderPresentAttendancePieChart(e,d,r),homePage.renderStudentFeesCountChart(e,h,g,f,m),homePage.renderStudentFeeHeadsCountChart(o,n,s),homePage.renderTransportAssignedPieChart(e,c,i),homePage.renderStaffGenderCountChart(e,l,u),homePage.renderStaffTodayAttendanceChart(e,p,b)},renderStudentCountChart:function(t,e){new Chart($("#chartjs-student-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Student Count",backgroundColor:PRIMARY_LOGO_COLOR,borderColor:PRIMARY_LOGO_COLOR,hoverBackgroundColor:PRIMARY_LOGO_COLOR,hoverBorderColor:PRIMARY_LOGO_COLOR,data:e,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]},animation:{onComplete:function(){var t=this.chart,e=t.ctx;e.textAlign="center",e.fillStyle="rgba(0, 0, 0, 1)",e.textBaseline="bottom",this.data.datasets.forEach(function(a,n){t.controller.getDatasetMeta(n).data.forEach(function(t,n){var o=a.data[n];e.fillText(o,t._model.x,t._model.y-5)})})}},events:[]}})},renderPresentAttendancePieChart:function(t,e,a){new Chart($("#chartjs-present-attendance-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(t,e,n)=>{var o=t.chart.width,s=t.chart.height,r=t.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,c=Math.round((o-r.measureText(i).width)/2),l=s/2;r.fillText(i,c,l),r.save()}}]})},renderTransportAssignedPieChart:function(t,e,a){new Chart($("#chartjs-active-transport-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(t,e,n)=>{var o=t.chart.width,s=t.chart.height,r=t.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,c=Math.round((o-r.measureText(i).width)/2),l=s/2;r.fillText(i,c,l),r.save()}}]})},renderStaffGenderCountChart:function(t,e,a){Chart.Legend.prototype.afterFit=function(){this.height=this.height+20};var n=new Chart($("#chartjs-genderwise-staff-bar-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Male Staff",backgroundColor:LOGO_COLOR,borderColor:LOGO_COLOR,hoverBackgroundColor:LOGO_COLOR,hoverBorderColor:LOGO_COLOR,data:e,barPercentage:.325,categoryPercentage:.5},{label:"Female Staff",backgroundColor:OLD_STUDENTS_FEMALE_COLOR,borderColor:OLD_STUDENTS_FEMALE_COLOR,hoverBackgroundColor:OLD_STUDENTS_FEMALE_COLOR,hoverBorderColor:OLD_STUDENTS_FEMALE_COLOR,data:a,barPercentage:.325,categoryPercentage:.5}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0,position:"top",align:"start",labels:{boxWidth:12}},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}});n.canvas.parentNode.style.height="311px",n.canvas.parentNode.style.width="311px"},renderStudentFeesCountChart:function(t,e,a,n,o){new Chart($("#chartjs-institute-fee-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Collected Fees",backgroundColor:window.theme.success,borderColor:window.theme.success,hoverBackgroundColor:window.theme.success,hoverBorderColor:window.theme.success,data:a,barPercentage:.325,categoryPercentage:.5},{label:"Discounted Fees",backgroundColor:window.theme.warning,borderColor:window.theme.warning,hoverBackgroundColor:window.theme.warning,hoverBorderColor:window.theme.warning,data:n,barPercentage:.325,categoryPercentage:.5},{label:"Due Fees",backgroundColor:window.theme.danger,borderColor:window.theme.danger,hoverBackgroundColor:window.theme.danger,hoverBorderColor:window.theme.danger,data:o,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderStudentFeeHeadsCountChart:function(t,e,a){const n=["#7bc4ce","#9dd4dc","#bfe4ea","#d4eef2","#e8f7f9","#f2fbfc"],o=e.map((e,o)=>{const s=n[o%n.length];return{label:e.split(":")[1],backgroundColor:s,borderColor:s,hoverBackgroundColor:s,hoverBorderColor:s,data:t.map(t=>a[t][e]||0),barPercentage:.325,categoryPercentage:.5}});new Chart($("#chartjs-institute-fee-head-distribution"),{type:"bar",data:{labels:t,datasets:o},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderStaffTodayAttendanceChart:function(t,e,a){new Chart($("#chartjs-staff-present-attendance-pie"),{type:"doughnut",data:{labels:t,datasets:[{data:e,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderColor:"transparent"}]},options:{rotation:1*Math.PI,circumference:1*Math.PI,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(t,e,n)=>{var o=t.chart.width,s=t.chart.height,r=t.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,c=Math.round((o-r.measureText(i).width)/2),l=s/2;r.fillText(i,c,l),r.save()}}]})},generateBirthdayCertificate:function(t){var e=JSON.parse($(t).find(".student-info").text()),a=e.instituteId,n=e.studentAcademicSessionInfoResponse.academicSession.academicSessionId,o=e.studentId;window.open(baseURL+"/organisation-portal/generate-birthday-certificate/"+a+"/"+n+"/"+o,"_blank")}};function loadChangePasswordPage(){ajaxClient.get("/organisation-portal/change-password",function(t){$("#main-content").html(t)})}function changePassword(){var t=$("#old-password").val(),e=$("#new-password").val(),a={oldPassword:t,newPassword:e};e==$("#confirm-new-password").val()?ajaxClient.post("/organisation-portal/update-password",{changePasswordInfo:JSON.stringify(a)},function(t){$("#change-password\\.status-modal-container").html(t),$("#change-password-status-modal").modal("toggle"),$("#old-password").val(""),$("#new-password").val(""),$("#confirm-new-password").val("")}):showErrorDialogBox("Password don't match!!")}function formatINRCurrency(t){return null==t||null==t||""==t?0:t=t.toLocaleString("en-IN")}function viewStatistics(){loadHomePage(),$("#sidebar").removeClass("collapsed")}function viewStatisticsV2(){homePageV2.loadHomePage(),$("#sidebar").removeClass("collapsed")}var menuLoader={registerSidebarMenu:function(){menuLoader.registerFeesReportsMenu()},registerFeesReportsMenu:function(){$("#orgFeesReportNav").on("click",function(){orgFeesReport.loadHomePage()})}},orgFeesReport={dataCache:{},loadHomePage:function(){ajaxClient.get("/organisation-portal/fees-reports",function(t){$("#main-content").html(t),initDateWithYearRange("-5:+5",!0),initSelect2("All"),orgFeesReport.initDataCache(),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),orgFeesReport.bindGenerateReportEvent(),reportUtils.bindSelectClassCheckboxEvent()})},initDataCache:function(){var t=readJson("#all-sessions"),e=readJson("#selected-academic-session-json");orgFeesReport.dataCache.allSessions=t,$(".report-academic-session").val(e.academicSessionId)},bindGenerateReportEvent:function(){$(".generate-report").on("click",function(){var t=$(this).closest("div.report-field-container");reportUtils.getReportHeadersCSV(t);if(!validateMandatoryFields($(t))){var e="";$(t).find(".report-academic-session option:selected").length>0&&(e=$(t).find(".report-academic-session option:selected").val()),e=""===e?0:e;var a=$(t).find("p.report-type").text().trim(),n="";$(t).find(".reports-student-status").length>0&&(n=$(t).find(".reports-student-status").val().join()),$(this).closest("div.modal").modal("toggle"),window.open(baseURL+"/organisation-portal/fees-generate-report/"+a+"?academic_session_id="+e+"&studentStatus="+n,"_blank")}})},initializeBirthdayWidget:function(){try{var t=document.getElementById("original-birthday-data");if(!t)return void console.log("Original birthday data not found");var e=JSON.parse(t.textContent);homePageV2.renderBirthdayWidget(e.todayBirthdays,e.upcomingBirthdays)}catch(t){console.error("Error initializing birthday widget:",t)}},filterBirthdayWidget:function(t){try{var e=document.getElementById("original-birthday-data");if(!e)return void console.log("Original birthday data not found");var a=JSON.parse(e.textContent);if(!t||0===t.length)return void homePageV2.renderBirthdayWidget(a.todayBirthdays,a.upcomingBirthdays);var n=Array.isArray(t)?t:[t];n=n.map(function(t){return parseInt(t)});var o=a.todayBirthdays.filter(function(t){return n.includes(parseInt(t.instituteId))}),s=a.upcomingBirthdays.filter(function(t){return n.includes(parseInt(t.instituteId))});homePageV2.renderBirthdayWidget(o,s)}catch(t){console.error("Error filtering birthday widget:",t)}},renderBirthdayWidget:function(t,e){$("#today-birthdays-count").text(t.length),$("#upcoming-birthdays-count").text(e.length),homePageV2.renderBirthdayList("#today-birthdays-container",t,"No birthdays today"),homePageV2.renderBirthdayList("#upcoming-birthdays-container",e,"No upcoming birthdays")},renderBirthdayList:function(t,e,a){var n=$(t);if(0!==e.length){var o='<div class="d-flex"><div class="w-100"><div class="mb-0">';e.forEach(function(t){var e=homePageV2.extractInitials(t.studentName);o+='<div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateBirthdayCertificate(this);">',o+='<p class="student-info" style="display:none;">'+JSON.stringify(t)+"</p>",o+='<div class="mt-3 ml-0 mr-2 mb-3 row">',o+='<div class="col-3" style="padding-right: 0px;">',o+='<div class="stat">',o+='<div style="text-align: center;">'+e+"</div>",o+="</div>",o+="</div>",o+='<div class="col-6 pb-0 mb-0 pt-1 pl-0">',o+='<p class="mb-0" style="font-size:14px;">'+t.studentName+" ("+t.admissionNumber+")</p>",o+='<p class="logo-color mb-0">'+homePageV2.formatBirthdayDate(t.birthdayDate)+"</p>",o+="</div>",o+='<div class="col-3">',o+='<p class="mb-0 pt-3" style="float:right">'+t.className+"</p>",o+="</div>",o+="</div>",o+="</div>"}),o+="</div></div></div>",n.html(o)}else n.html('<div class="text-center py-4"><p class="text-muted">'+a+"</p></div>")},extractInitials:function(t){return t?t.split(" ").map(function(t){return t.charAt(0).toUpperCase()}).join(""):""},formatBirthdayDate:function(t){if("number"==typeof t){var e=new Date(1e3*t);return String(e.getDate()).padStart(2,"0")+" "+["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][e.getMonth()]+" "+e.getFullYear()}return t}};