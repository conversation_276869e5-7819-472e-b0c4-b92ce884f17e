import time
import json
from core.controller.utils.restclient import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *

def get_birthday_certificate(user_login_view, institute_id, academic_session_id, student_id):
    return restclient.get_file(user_login_view, "/2.0/student/"+student_id+"/birthday-certificate?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id))

def get_fees_report_data(user_login_view, organisation_id, report_data):
    user = user_login_view['user']
    student_status = report_data['studentStatus']
    return restclient.get(user_login_view, "/2.0/organisation/reports/"+str(organisation_id)+"/view-report/"+report_data['report_type']+"?academic_session_id="+str(report_data['academic_session_id'])+"&student_status="+student_status + "&user_id="+user['uuid']).get_data()


def get_fees_report(user_login_view, organisation_id, report_data):
    user = user_login_view['user']
    student_status = report_data['studentStatus']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/organisation/reports/"+str(organisation_id)+"/generate-report/"+report_data['report_type']+"?academic_session_id="+str(report_data['academic_session_id'])+"&student_status="+student_status + "&user_id="+user['uuid'] + "&download_format="+str(report_data['download_format']))

def get_birthday_stats(user_login_view, organisation_id, selected_institute_ids):
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.1/dashboards/users/student/organisation/{organisation_id}/birthday?institute_ids={institute_ids}&user_id={user_id}".format(organisation_id = organisation_id, institute_ids = selected_institute_ids, user_id = user['uuid'])).get_data()

def get_student_attendance_org_stats(user_login_view, organisation_id, selected_institute_ids, date):
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.1/dashboards/attendance/organisation/{organisation_id}/students?institute_ids={institute_ids}&date={date}&user_id={user_id}".format(organisation_id = organisation_id, institute_ids = selected_institute_ids, date = date, user_id = user['uuid'])).get_data()


def get_staff_attendance_org_stats(user_login_view, organisation_id, selected_institute_ids, date):
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.1/dashboards/attendance/organisation/{organisation_id}/staff?institute_ids={institute_ids}&date={date}&user_id={user_id}".format(organisation_id = organisation_id, institute_ids = selected_institute_ids, date = date, user_id = user['uuid'])).get_data()


def get_student_admission_org_stats(user_login_view, organisation_id, selected_institute_ids, start_date, end_date):
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.1/dashboards/users/student/organisation/{organisation_id}/admission?institute_ids={institute_ids}&start_date={start_date}&end_date={end_date}&user_id={user_id}".format(organisation_id = organisation_id, institute_ids = selected_institute_ids, start_date = start_date, end_date = end_date, user_id = user['uuid'])).get_data()


def get_student_admission_org_stats(user_login_view, organisation_id, selected_institute_ids, start_date, end_date):
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.1/dashboards/users/student/organisation/{organisation_id}/admission?institute_ids={institute_ids}&start_date={start_date}&end_date={end_date}&user_id={user_id}".format(organisation_id = organisation_id, institute_ids = selected_institute_ids, start_date = start_date, end_date = end_date, user_id = user['uuid'])).get_data()


def get_fee_collection_org_stats(user_login_view, organisation_id, selected_institute_ids, start_date, end_date):
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.1/dashboards/fees/organisation/{organisation_id}?institute_ids={institute_ids}&start_date={start_date}&end_date={end_date}&user_id={user_id}".format(organisation_id = organisation_id, institute_ids = selected_institute_ids, start_date = start_date, end_date = end_date, user_id = user['uuid'])).get_data()

def get_active_student_transport_stats(user_login_view, organisation_id, selected_institute_ids, date):
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.1/dashboards/users/student/organisation/{organisation_id}/transport?institute_ids={institute_ids}&date={date}&user_id={user_id}".format(organisation_id = organisation_id, institute_ids = selected_institute_ids, date = date, user_id = user['uuid'])).get_data()